import { Elysia, t } from "elysia";
import { webhookLogic } from "../services/handleWebhookLogic";

export const vedaAssessmentRoute = new Elysia({
	prefix: "/veda-assessment",
}).post(
	"/result/:candidateId",
	async ({ params, body }) => {
		const { candidateId } = params;
		const { outcome, reasoning } = body;

		console.log(
			`Received 'veda-review' webhook for candidate ${candidateId} with outcome: ${outcome}`,
		);

		await webhookLogic(candidateId, "assessment", outcome, reasoning);

		return { message: "Veda review webhook processed successfully." };
	},
	{
		params: t.Object({ candidateId: t.String() }),
		body: t.Object({
			outcome: t.String(),
			reasoning: t.Optional(t.Any()),
		}),
	},
);
