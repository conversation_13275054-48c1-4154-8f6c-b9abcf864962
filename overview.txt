1. Introduction

The goal was to design and build a system capable of managing a multi-stage, automated hiring workflow. The system needed to be robust enough to handle tasks of varying complexity and duration, from instantaneous LLM calls to long-running, asynchronous processes involving third-party services and human-in-the-loop delays.

Key requirements that shaped this architecture include:

    Asynchronous Operations: Many tasks, like phone screenings, rely on external services and receive results via webhooks at unpredictable times.
    Custom Scheduling: Certain tasks must only execute during specific time windows (e.g., business hours).
    Resilience & Fault Tolerance: The system must gracefully handle temporary failures (e.g., network issues, database glitches) without losing data or performing duplicate, costly actions.
    Extensibility: The business team must be able to easily add new stages or modify the workflow logic without requiring significant code changes.

This document details the event-driven architecture designed to meet these challenges.
2. Core Architectural Principles

Our design is built on four fundamental principles that ensure the system is scalable, resilient, and maintainable.
a. Database as the Single Source of Truth

Instead of relying on the state of a job in the queue, the Candidate model in MongoDB is the definitive source of truth. Each candidate has a stage and status field (e.g., QUEUED, AWAITING_RESULT) that provides a clear, real-time snapshot of their position in the workflow. This simplifies state management and makes debugging and reporting far more reliable.
b. Event-Driven Orchestration via "Pause & Resume"

The entire system operates on a "Pause & Resume" model. A worker's job is not to complete a stage from start to finish, but merely to initiate a task. After initiation, the workflow for that candidate effectively pauses. It only resumes when an external event—typically a webhook call—arrives with the result. This is the cornerstone of handling true asynchronicity.
c. Declarative, Config-Driven Workflows

All workflow logic—the sequence of stages, the conditions for moving between them, and custom scheduling rules—is stored in the JobConfig collection in MongoDB. This declarative approach means the workflow is defined by data, not code. To change the screening schedule or add a new "assessment" stage, an administrator simply updates a database document, making the system incredibly flexible.
d. Decoupling with a Message Queue

We use BullMQ and Redis to decouple the main application (the API server) from the background task processors (the workers). When a task needs to be done, it's added to a queue as a message. This allows the API to remain fast and responsive while the workers process heavy or long-running tasks independently in the background.
3. System Components & Flow Diagram

The architecture consists of several specialized components that interact in a well-defined sequence.

    API Server (Elysia): The public face of the system. Its only roles are to receive initial commands (like /start-workflow) and to act as the listener for incoming webhooks.
    The Orchestrator (triggerNextStage): The "brain" of the system. This central function is called only by the API server (either at the start or from a webhook). It reads the JobConfig, applies scheduling logic, and enqueues the next task.
    Redis (BullMQ): The message broker. It holds the queues for each stage and manages delayed jobs, ensuring tasks are processed at the correct time.
    The Worker: A background process that listens to a specific queue. Its sole responsibility is to pick up a job, update the candidate's status to IN_PROGRESS, and call the appropriate agent to initiate the task.
    The Agents: Specialized modules that perform the actual work (e.g., runVedaReview, runScreening). They contain the logic for interacting with LLMs or third-party APIs.
    The Webhook Handlers: API endpoints that act as the "ears." They receive results, update the final status of the AgentTask and Candidate, and then call the Orchestrator to continue the workflow, thus completing the loop.

4. How It Works: A Candidate's Journey

Let's trace a candidate's path through a veda-review stage (synchronous) followed by a screening stage (asynchronous, business hours only).

1. Initiation:

    An admin sends a POST /start-workflow request.
    The API calls the Orchestrator (triggerNextStage) for the candidate with the outcome "START".
    The Orchestrator reads the JobConfig, sees the first stage is veda-review, checks its IMMEDIATE scheduling, calculates a 0ms delay, and adds a job to the veda-review queue in Redis.
    It updates the Candidate's status to QUEUED.

2. Veda Review (Synchronous "Self-Webhook"):

    The Worker for the veda-review queue instantly picks up the job.
    It updates the Candidate's status to IN_PROGRESS and creates an AgentTask.
    It calls the runVedaReview Agent.
    The agent calls an LLM, gets a result ("best"), and immediately makes a POST request to its own system's /webhook/veda-review/result/:candidateId endpoint.
    The worker updates the Candidate's status to AWAITING_RESULT and its job is done.

3. Transition to Screening:

    The Webhook Handler receives the result. It updates the AgentTask to COMPLETED and the Candidate's status to COMPLETED_SUCCESS.
    Crucially, it calls the Orchestrator again, this time with the outcome "best".
    The Orchestrator reads the JobConfig, sees that "best" leads to the screening stage. It finds the BUSINESS_HOURS scheduling rule. It calculates the necessary delay (e.g., 14 hours if it's currently 8 PM) and adds a job to the screening queue with that delay.
    It updates the Candidate's status to QUEUED.

4. Screening (Asynchronous):

    After the delay expires, the Worker for the screening queue picks up the job.
    It updates the Candidate's status to IN_PROGRESS and calls the runScreening Agent.
    The agent calls the external phone service, providing it with a unique webhook URL (/webhook/screening/result/:candidateId).
    The worker updates the Candidate's status to AWAITING_RESULT. The workflow is now paused.

5. Completion:

    Hours or days later, the external service calls the webhook with the outcome ("pass").
    The Webhook Handler receives it, updates the relevant AgentTask and Candidate documents, and calls the Orchestrator one last time.
    The Orchestrator sees that "pass" leads to a terminal stage ("stop"). It updates the Candidate's status to WORKFLOW_TERMINATED, and the process for this candidate is complete.

5. Architectural Benefits

    Resilience: The "self-webhook" pattern and the separation of concerns prevent costly duplicate actions. A failure during orchestration does not cause the initial task (e.g., an LLM call) to be retried.
    Scalability: If the screening stage becomes a bottleneck, you can scale the number of screening worker processes independently without affecting any other part of the system.
    Maintainability: The logic for each component is small, focused, and predictable. When a bug occurs, it's easy to isolate whether the issue is in task initiation (worker), workflow routing (orchestrator), or result processing (webhook).
    Flexibility: Business logic is stored as data. Adding a new agent and stage is as simple as creating a new agent file, a new webhook handler, and adding a new configuration document to the database.

