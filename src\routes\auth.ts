import Elysia, { t } from "elysia";
import { Recruiter } from "../models/recruiter.model";

export const authApp = new Elysia({ prefix: "/auth" }).post(
	"/login",
	async ({ body, cookie }) => {
		const recruiter = await Recruiter.findOne({ email: body.email });
		if (!recruiter) return { error: "User not found" };
		if (!(await recruiter.comparePassword(body.password)))
			return { error: "Invalid password" };
		const token = recruiter.generateToken();
		cookie.authToken?.set({
			value: token,
		});
		return { token };
	},
	{
		body: t.Object({
			email: t.String(),
			password: t.String(),
		}),
	},
);
