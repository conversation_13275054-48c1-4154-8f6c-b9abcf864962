import mongoose from "mongoose";
import { connectToDatabase } from "../config/mongodb"; // Assuming your MongoDB connection utility
import Job from "../models/job.model";
import {
	ICommunicationChannel,
	JobConfigModel,
} from "../models/jobConfig.model"; // Adjust path as needed

// Ensure the database connection is established before proceeding
await connectToDatabase();

async function createVedaScreeningJobConfig() {
	console.log("Attempting to create Veda-Screening JobConfig...");
	const firstjob = await Job.findOne({});

	if (!firstjob) {
		console.error("No job found");
		return;
	}

	const vedaScreeningJobConfig = {
		jobId: firstjob._id,
		flow: [
			{
				stage: "veda-review",
				next: [
					{ stage: "screening", outcome: "best" }, // If Veda review passes, move to screening
					{ stage: "screening", outcome: "good" }, // If Veda review passes, move to screening
					{ stage: "rejected", outcome: "bad" }, // If <PERSON><PERSON> review fails, reject
				],
			},
			{
				stage: "screening",
				next: [
					{ stage: "assessment", outcome: "pass" }, // If screening passes, consider hired
					{ stage: "rejected", outcome: "fail" }, // If screening fails, reject
				],
			},
			{
				stage: "assessment",
				next: [
					{ stage: "stop", outcome: "qualified" },
					{ stage: "rejected", outcome: "not-qualified" },
				],
			},
		],
		stageConfig: [
			{
				stage: "veda-review",
				action: {
					agentId: "reviewAgent",
					outputs: ["best", "good", "bad"],
					params: {
						reviewCriteria:
							"Match candidate profile against job description keywords and basic qualifications.",
						minimumScore: 70,
					},
				},
			},
			{
				stage: "screening",
				action: {
					agentId: "screeningAgent", // The agentId responsible for the initial screening
					outputs: ["pass", "fail"], // Expected outcomes from the screeningAgent
					params: {
						screeningQuestions: [
							{
								id: 1,
								question: "Are you comfortable with remote work?",
								options: ["Yes", "No"],
								correctAnswer: "Yes",
							},
							{
								id: 2,
								question: "Are you comfortable with 10 LPA?",
								options: ["Yes", "No"],
								correctAnswer: "Yes",
							},
						],
					},
				},
				CommunicationChannel: ICommunicationChannel.PLIVO,
			},
			{
				stage: "assessment",
				action: {
					agentId: "interviewAgent",
					output: ["qualified", "not-qualified"],
					params: {
						skills: ["reactjs", "nodejs", "mongodb"],
					},
				},
				communicationChannel: ICommunicationChannel.EMAIL,
			},
		],
	};

	try {
		// create a new jobConfig documnet:
		const createdConfig = await JobConfigModel.create(vedaScreeningJobConfig);

		console.log("Successfully created/updated JobConfig:", createdConfig.id);
		console.log(JSON.stringify(createdConfig, null, 2)); // Log the full config for verification
	} catch (err) {
		if (err instanceof Error) {
			console.error("Error creating/updating JobConfig:", err.message);
		} else {
			console.error("An unknown error occurred:", err);
		}
		process.exit(1); // Exit with an error code
	} finally {
		// Ensure the database connection is closed
		await mongoose.disconnect();
		console.log("Database connection closed.");
	}
}

// Execute the function
createVedaScreeningJobConfig().catch((err) => {
	console.error("Unhandled error in script execution:", err);
	process.exit(1);
});
