import Elysia, { t } from "elysia";
import Candidate from "../models/candidate.model";
import { fetchExternalCandidates } from "../services/mockExternalATS.service";
import { startJobWorkflow } from "../services/workFlow.service";

export const jobRoutes = new Elysia({ prefix: "/job" })
	.get(
		"/jobs/:jobId/candidates",
		async ({ params, set }) => {
			const { jobId } = params;
			console.log(`Received request for job ID: ${jobId} (from local DB)`);

			try {
				const candidates = await Candidate.find({}).select(
					"-contactInfo.email",
				);

				if (candidates.length === 0) {
					set.status = 404;
					return {
						message:
							"No lead candidates found for this job ID (or no active candidates in system).",
					};
				}

				return {
					jobId,
					candidateCount: candidates.length,
					candidates: candidates.map((candidate) => ({
						_id: candidate._id,
						name: candidate.name,
						email: candidate.email,
						currentStage: candidate.stage,
						resumeLink: candidate.resumeLink,
						source: candidate.source,
						expectedSalary: candidate.expectedSalary,
						contactInfo: {
							phone: candidate.contactInfo.phone,
							linkedin: candidate.contactInfo.linkedin,
							github: candidate.contactInfo.github,
							address: candidate.contactInfo.address,
						},
					})),
				};
			} catch (error) {
				console.error("Error fetching candidates:", error);
				set.status = 500;
				return { message: "Internal server error" };
			}
		},
		{
			params: t.Object({
				jobId: t.String(),
			}),
			detail: {
				summary: "Get lead candidates for a specific job ID (from local DB)",
				description:
					"Mimics pulling lead candidates from an ATS for a given job ID. Currently returns all active candidates from local DB as a placeholder.",
				tags: ["Candidates"],
			},
		},
	)
	.get(
		"/external-jobs/:jobId/candidates",
		async ({ params, set }) => {
			const { jobId } = params;
			console.log(
				`Received request for job ID: ${jobId} (from mocked external API)`,
			);

			try {
				// Call the mock external API service
				const externalCandidates = await fetchExternalCandidates(jobId);

				if (externalCandidates.length === 0) {
					set.status = 404;
					return {
						message:
							"No lead candidates found from the mocked external API for this job ID.",
					};
				}

				// Format the response to match your ICandidate structure (if needed)
				const formattedCandidates = externalCandidates.map((candidate) => ({
					_id: candidate._id, // Use the generated mock ID
					name: candidate.name,
					email: candidate.email,
					currentStage: candidate.stage,
					resumeLink: candidate.resumeLink,
					source: candidate.source,
					expectedSalary: candidate.expectedSalary,
					contactInfo: {
						email: candidate.contactInfo?.email || "", // Ensure email is present from external mock
						phone: candidate.contactInfo?.phone,
						linkedin: candidate.contactInfo?.linkedin,
						github: candidate.contactInfo?.github,
						address: candidate.contactInfo?.address,
					},
				}));

				return {
					jobId,
					candidateCount: formattedCandidates.length,
					candidates: formattedCandidates,
				};
			} catch (error) {
				console.error(
					"Error fetching candidates from mocked external API:",
					error,
				);
				set.status = 500;
				return {
					message: "Internal server error when fetching from external API.",
				};
			}
		},
		{
			params: t.Object({
				jobId: t.String(),
			}),
			detail: {
				summary:
					"Get lead candidates for a specific job ID (from Mocked External API)",
				description:
					"Mimics pulling lead candidates from an external ATS for a given job ID using a mock service. Returns at least 10 candidates.",
				tags: ["External Candidates"],
			},
		},
	)
	.post(
		"/workflows/:jobId/start",
		async ({ params }) => {
			const { jobId } = params;
			return await startJobWorkflow(jobId);
		},
		{
			params: t.Object({
				jobId: t.String(),
			}),
		},
	);
