{"name": "veda", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch src/index.ts", "workers": "bun run --watch src/run-workers.ts", "lint": "biome check", "seed:candidates": "bun --env-file=.env run src/scripts/dummy-data-injection/seedCandidates.ts"}, "devDependencies": {"@types/bun": "latest", "@types/ioredis": "^5.0.0", "biome": "^0.3.3"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@elysiajs/swagger": "^1.3.1", "axios": "^1.11.0", "bullmq": "^5.56.5", "date-fns-tz": "^3.2.0", "elysia": "^1.3.5", "envalid": "^8.1.0", "ioredis": "^5.6.1", "mongoose": "^8.16.4", "openai": "^5.10.2", "unpdf": "^1.1.0", "zod": "^4.0.8"}}