import { Elysia, t } from "elysia";
import { env } from "../config/env";
import { AgentTask, TaskStatus } from "../models/agentTask.model";
import { webhookLogic } from "../services/handleWebhookLogic";
import { plivoScreeningCallService } from "../services/plivoScreeningCall";

export const vedaScreeningRoute = new Elysia({
	prefix: "/veda-screening",
})
	.post(
		"/update-status",
		async ({ body }) => {
			const { candidateId, phoneNumber, status, screeningCleared } = body;

			console.log(phoneNumber, status, screeningCleared);

			if (screeningCleared !== undefined) {
				await webhookLogic(candidateId, "screening", screeningCleared, {
					plivoCallStatus: "completed",
				});
			}

			const agentTask = await AgentTask.findOneAndUpdate(
				{
					candidateId,
					stage: "screening",
					status: TaskStatus.IN_PROGRESS, // Find the task that was started but not finished
				},
				{
					status: TaskStatus.COMPLETED,
					result: { plivoCallStatus: status },
					finishedAt: new Date(),
				},
				{ sort: { createdAt: -1 } }, // Get the most recent one
			);

			if (!agentTask) {
				throw new Error(
					`Webhook Error: Could not find an active AgentTask for candidate ${candidateId} at stage screening stage}`,
				);
			}

			return {
				status: true,
				message: "Status updated successfully",
			};
		},
		{
			body: t.Object({
				candidateId: t.String(),
				phoneNumber: t.String(),
				status: t.String(),
				screeningCleared: t.Optional(t.String()),
			}),
		},
	)
	.post(
		"/trigger-screening-call",
		async ({ body }) => {
			const { questions, companyName, role, to } = body;

			const webhook_endpoint = env.API_URL.replace("https://", "").replace(
				"http://",
				"",
			);

			const formattedTo = plivoScreeningCallService.formatPhoneNumber(to);

			const payload = {
				questions,
				companyName,
				role,
				to: formattedTo,
				webhook_endpoint,
			};

			console.log(payload);

			const response =
				await plivoScreeningCallService.initiateScreeningCall(payload);

			return {
				status: response.success,
				message: response.message,
			};
		},
		{
			body: t.Object({
				questions: t.String(),
				companyName: t.String(),
				role: t.String(),
				to: t.String(),
			}),
		},
	);
